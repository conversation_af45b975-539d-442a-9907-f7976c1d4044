// Angular Material Theme - Using prebuilt theme with proper dark mode support
@import "@angular/material/prebuilt-themes/azure-blue.css";

// Global styles - minimal setup for Angular Material
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  font-family: "Roboto", "Helvetica Neue", sans-serif;
}

.spacer {
  flex: 1 1 auto;
}

// Spacing utilities for consistent layout
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.content-spacing {
  > * + * {
    margin-top: 16px;
  }
}

.card-spacing {
  margin-bottom: 16px;
}

.form-spacing {
  mat-form-field {
    width: 100%;
    margin-bottom: 16px;
  }
}

.section-spacing {
  margin: 24px 0;
}

.tight-spacing {
  > * + * {
    margin-top: 8px;
  }
}

// Dark mode improvements
@media (prefers-color-scheme: dark) {
  .mat-mdc-card {
    background-color: #2d2d2d;
  }

  .mat-mdc-tab-header {
    background-color: #1e1e1e;
  }
}

// Manual dark theme class
.dark-theme {
  .mat-mdc-card {
    background-color: #2d2d2d !important;
    color: #ffffff;
  }

  .mat-mdc-tab-header {
    background-color: #1e1e1e !important;
  }

  .mat-toolbar {
    background-color: #1976d2 !important;
    color: #ffffff;
  }

  .mat-mdc-list-item {
    color: #ffffff;
  }

  .mat-mdc-form-field {
    color: #ffffff;
  }

  .mat-mdc-card-title,
  .mat-mdc-card-subtitle {
    color: #ffffff !important;
  }

  h4 {
    color: rgba(255, 255, 255, 0.6) !important;
  }
}
