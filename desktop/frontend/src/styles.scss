// Angular Material Theme - Using prebuilt theme with proper dark mode support
@import "@angular/material/prebuilt-themes/azure-blue.css";

// Dark theme variables
:root {
  --background-color: #ffffff;
  --surface-color: #ffffff;
  --primary-text: rgba(0, 0, 0, 0.87);
  --secondary-text: rgba(0, 0, 0, 0.6);
  --border-color: rgba(0, 0, 0, 0.12);
  --hover-color: rgba(0, 0, 0, 0.04);
  --card-background: #ffffff;
  --toolbar-background: #1976d2;
  --toolbar-text: #ffffff;
}

// Global styles - minimal setup for Angular Material
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  font-family: "Roboto", "Helvetica Neue", sans-serif;
  background-color: var(--background-color);
  color: var(--primary-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

// Smooth transitions for all elements
* {
  transition: background-color 0.3s ease, color 0.3s ease,
    border-color 0.3s ease;
}

.spacer {
  flex: 1 1 auto;
}

// Spacing utilities for consistent layout
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.content-spacing {
  > * + * {
    margin-top: 16px;
  }
}

.card-spacing {
  margin-bottom: 16px;
}

.form-spacing {
  mat-form-field {
    width: 100%;
    margin-bottom: 16px;
  }
}

.section-spacing {
  margin: 24px 0;
}

.tight-spacing {
  > * + * {
    margin-top: 8px;
  }
}

// Dark theme variables for dark mode
.dark-theme {
  --background-color: #121212;
  --surface-color: #1e1e1e;
  --primary-text: rgba(255, 255, 255, 0.87);
  --secondary-text: rgba(255, 255, 255, 0.6);
  --border-color: rgba(255, 255, 255, 0.12);
  --hover-color: rgba(255, 255, 255, 0.04);
  --card-background: #1e1e1e;
  --toolbar-background: #1976d2;
  --toolbar-text: #ffffff;
}

// System dark mode preference
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #121212;
    --surface-color: #1e1e1e;
    --primary-text: rgba(255, 255, 255, 0.87);
    --secondary-text: rgba(255, 255, 255, 0.6);
    --border-color: rgba(255, 255, 255, 0.12);
    --hover-color: rgba(255, 255, 255, 0.04);
    --card-background: #1e1e1e;
    --toolbar-background: #1976d2;
    --toolbar-text: #ffffff;
  }
}

// Comprehensive dark theme styles
.dark-theme {
  // Material Design Components
  .mat-mdc-card {
    background-color: var(--card-background) !important;
    color: var(--primary-text) !important;
    border: 1px solid var(--border-color);
  }

  .mat-mdc-tab-header {
    background-color: var(--surface-color) !important;
    border-top: 1px solid var(--border-color);
  }

  .mat-mdc-tab-label {
    color: var(--secondary-text) !important;
  }

  .mat-mdc-tab-label.mdc-tab--active {
    color: var(--primary-text) !important;
  }

  .mat-toolbar {
    background-color: var(--toolbar-background) !important;
    color: var(--toolbar-text) !important;
  }

  .mat-mdc-list-item {
    color: var(--primary-text) !important;
    background-color: transparent !important;
  }

  .mat-mdc-list-item:hover {
    background-color: var(--hover-color) !important;
  }

  .mat-mdc-form-field {
    color: var(--primary-text) !important;
  }

  .mat-mdc-form-field .mat-mdc-form-field-label {
    color: var(--secondary-text) !important;
  }

  .mat-mdc-form-field.mat-focused .mat-mdc-form-field-label {
    color: #1976d2 !important;
  }

  .mat-mdc-input-element {
    color: var(--primary-text) !important;
  }

  .mat-mdc-select-value {
    color: var(--primary-text) !important;
  }

  .mat-mdc-card-title {
    color: var(--primary-text) !important;
  }

  .mat-mdc-card-subtitle {
    color: var(--secondary-text) !important;
  }

  .mat-mdc-card-content {
    color: var(--primary-text) !important;
  }

  .mat-mdc-button {
    color: var(--primary-text) !important;
  }

  .mat-mdc-icon-button {
    color: var(--primary-text) !important;
  }

  .mat-mdc-fab {
    background-color: #1976d2 !important;
    color: #ffffff !important;
  }

  // Custom elements
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: var(--primary-text) !important;
  }

  p {
    color: var(--primary-text) !important;
  }

  // Form field outlines
  .mat-mdc-form-field-outline {
    color: var(--border-color) !important;
  }

  .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
    color: #1976d2 !important;
  }

  // Dividers
  .mat-divider {
    border-top-color: var(--border-color) !important;
  }

  // Tooltips
  .mat-mdc-tooltip {
    background-color: var(--surface-color) !important;
    color: var(--primary-text) !important;
  }

  // Snackbars
  .mat-mdc-snack-bar-container {
    background-color: var(--surface-color) !important;
    color: var(--primary-text) !important;
  }

  // Dialogs
  .mat-mdc-dialog-surface {
    background-color: var(--surface-color) !important;
    color: var(--primary-text) !important;
  }

  // Menu panels
  .mat-mdc-menu-panel {
    background-color: var(--surface-color) !important;
  }

  .mat-mdc-menu-item {
    color: var(--primary-text) !important;
  }

  .mat-mdc-menu-item:hover {
    background-color: var(--hover-color) !important;
  }

  // Select panels
  .mat-mdc-select-panel {
    background-color: var(--surface-color) !important;
  }

  .mat-mdc-option {
    color: var(--primary-text) !important;
  }

  .mat-mdc-option:hover {
    background-color: var(--hover-color) !important;
  }

  .mat-mdc-option.mat-mdc-option-active {
    background-color: var(--hover-color) !important;
  }
}

// System dark mode preference styles
@media (prefers-color-scheme: dark) {
  // Material Design Components
  .mat-mdc-card {
    background-color: var(--card-background) !important;
    color: var(--primary-text) !important;
    border: 1px solid var(--border-color);
  }

  .mat-mdc-tab-header {
    background-color: var(--surface-color) !important;
    border-top: 1px solid var(--border-color);
  }

  .mat-mdc-tab-label {
    color: var(--secondary-text) !important;
  }

  .mat-mdc-tab-label.mdc-tab--active {
    color: var(--primary-text) !important;
  }

  .mat-toolbar {
    background-color: var(--toolbar-background) !important;
    color: var(--toolbar-text) !important;
  }

  .mat-mdc-list-item {
    color: var(--primary-text) !important;
    background-color: transparent !important;
  }

  .mat-mdc-list-item:hover {
    background-color: var(--hover-color) !important;
  }

  .mat-mdc-form-field {
    color: var(--primary-text) !important;
  }

  .mat-mdc-form-field .mat-mdc-form-field-label {
    color: var(--secondary-text) !important;
  }

  .mat-mdc-form-field.mat-focused .mat-mdc-form-field-label {
    color: #1976d2 !important;
  }

  .mat-mdc-input-element {
    color: var(--primary-text) !important;
  }

  .mat-mdc-select-value {
    color: var(--primary-text) !important;
  }

  .mat-mdc-card-title {
    color: var(--primary-text) !important;
  }

  .mat-mdc-card-subtitle {
    color: var(--secondary-text) !important;
  }

  .mat-mdc-card-content {
    color: var(--primary-text) !important;
  }

  .mat-mdc-button {
    color: var(--primary-text) !important;
  }

  .mat-mdc-icon-button {
    color: var(--primary-text) !important;
  }

  .mat-mdc-fab {
    background-color: #1976d2 !important;
    color: #ffffff !important;
  }

  // Custom elements
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: var(--primary-text) !important;
  }

  p {
    color: var(--primary-text) !important;
  }

  // Form field outlines
  .mat-mdc-form-field-outline {
    color: var(--border-color) !important;
  }

  .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
    color: #1976d2 !important;
  }

  // Dividers
  .mat-divider {
    border-top-color: var(--border-color) !important;
  }

  // Tooltips
  .mat-mdc-tooltip {
    background-color: var(--surface-color) !important;
    color: var(--primary-text) !important;
  }

  // Snackbars
  .mat-mdc-snack-bar-container {
    background-color: var(--surface-color) !important;
    color: var(--primary-text) !important;
  }

  // Dialogs
  .mat-mdc-dialog-surface {
    background-color: var(--surface-color) !important;
    color: var(--primary-text) !important;
  }

  // Menu panels
  .mat-mdc-menu-panel {
    background-color: var(--surface-color) !important;
  }

  .mat-mdc-menu-item {
    color: var(--primary-text) !important;
  }

  .mat-mdc-menu-item:hover {
    background-color: var(--hover-color) !important;
  }

  // Select panels
  .mat-mdc-select-panel {
    background-color: var(--surface-color) !important;
  }

  .mat-mdc-option {
    color: var(--primary-text) !important;
  }

  .mat-mdc-option:hover {
    background-color: var(--hover-color) !important;
  }

  .mat-mdc-option.mat-mdc-option-active {
    background-color: var(--hover-color) !important;
  }
}
