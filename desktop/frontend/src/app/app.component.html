<!-- Modern App Layout with Bottom Tabs -->
<div class="app-container">
  <!-- App Bar -->
  <mat-toolbar color="primary">
    <mat-icon>storage</mat-icon>
    <span>NS Drive</span>

    <span class="spacer"></span>

    <!-- Action Buttons -->
    <button mat-icon-button matTooltip="Settings">
      <mat-icon>settings</mat-icon>
    </button>

    <button mat-icon-button matTooltip="Help">
      <mat-icon>help</mat-icon>
    </button>
  </mat-toolbar>

  <!-- Main Content with Bottom Tabs -->
  <div class="content-container">
    <mat-tab-group
      [selectedIndex]="
        (tab$ | async) === 'home' ? 0 : (tab$ | async) === 'profiles' ? 1 : 2
      "
      (selectedIndexChange)="onTabChange($event)"
      headerPosition="below"
      animationDuration="300ms"
    >
      <!-- Operations Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>dashboard</mat-icon>
          <span>Operations</span>
        </ng-template>
        <app-home></app-home>
      </mat-tab>

      <!-- Profiles Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>folder_shared</mat-icon>
          <span>Profiles</span>
        </ng-template>
        <app-profiles></app-profiles>
      </mat-tab>

      <!-- Remotes Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>cloud_queue</mat-icon>
          <span>Remotes</span>
        </ng-template>
        <app-remotes></app-remotes>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
