<!-- Modern App Layout with Angular Material -->
<mat-sidenav-container>
  <!-- Navigation Drawer -->
  <mat-sidenav
    #drawer
    [mode]="isHandset ? 'over' : 'side'"
    [opened]="!isHandset"
    fixedInViewport="true"
    [disableClose]="!isHandset"
  >
    <!-- Drawer Header -->
    <mat-toolbar>
      <mat-icon>storage</mat-icon>
      <span>NS Drive</span>
    </mat-toolbar>

    <!-- Navigation Menu -->
    <mat-nav-list>
      <a
        mat-list-item
        href="javascript:void(0)"
        (click)="openHome(); isHandset && drawer.close()"
        [activated]="(tab$ | async) === 'home'"
      >
        <mat-icon matListItemIcon>dashboard</mat-icon>
        <span matListItemTitle>Operations</span>
      </a>

      <a
        mat-list-item
        href="javascript:void(0)"
        (click)="openProfiles(); isHandset && drawer.close()"
        [activated]="(tab$ | async) === 'profiles'"
      >
        <mat-icon matListItemIcon>folder_shared</mat-icon>
        <span matListItemTitle>Profiles</span>
      </a>

      <a
        mat-list-item
        href="javascript:void(0)"
        (click)="openRemotes(); isHandset && drawer.close()"
        [activated]="(tab$ | async) === 'remotes'"
      >
        <mat-icon matListItemIcon>cloud_queue</mat-icon>
        <span matListItemTitle>Remote Manager</span>
      </a>

      <mat-divider></mat-divider>
    </mat-nav-list>
  </mat-sidenav>

  <!-- Main Content -->
  <mat-sidenav-content>
      <!-- App Bar -->
      <mat-toolbar color="primary">
        <button mat-icon-button (click)="drawer.toggle()" *ngIf="isHandset">
          <mat-icon>menu</mat-icon>
        </button>

        <span>
          {{
            (tab$ | async) === "home"
              ? "Dashboard"
              : (tab$ | async) === "profiles"
              ? "Sync Profiles"
              : (tab$ | async) === "remotes"
              ? "Cloud Storage"
              : "NS Drive"
          }}
        </span>

        <span class="spacer"></span>

        <!-- Action Buttons -->
        <button mat-icon-button matTooltip="Settings">
          <mat-icon>settings</mat-icon>
        </button>

        <button mat-icon-button matTooltip="Help">
          <mat-icon>help</mat-icon>
        </button>
      </mat-toolbar>

      <!-- Page Content Container -->
      <main [ngSwitch]="tab$ | async">
        <app-home *ngSwitchCase="'home'"></app-home>
        <app-profiles *ngSwitchCase="'profiles'"></app-profiles>
        <app-remotes *ngSwitchCase="'remotes'"></app-remotes>
      </main>
  </mat-sidenav-content>
</mat-sidenav-container>
