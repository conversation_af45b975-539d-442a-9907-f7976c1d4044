import { CommonModule } from "@angular/common";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from "@angular/core";
import { Action, AppService } from "./app.service.js";
import { BehaviorSubject, combineLatest, Subscription } from "rxjs";
import { HomeComponent } from "./home/<USER>";
import { models } from "../../wailsjs/go/models.js";
import { ProfilesComponent } from "./profiles/profiles.component.js";
import { ProfileEditComponent } from "./profiles/profile-edit.component.js";
import { RemotesComponent } from "./remotes/remotes.component.js";
import { NavigationService } from "./navigation.service.js";

// Material Design imports
import { MatToolbarModule } from "@angular/material/toolbar";
import { MatIconModule } from "@angular/material/icon";
import { MatButtonModule } from "@angular/material/button";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatTabsModule } from "@angular/material/tabs";

type Tab = "home" | "profiles" | "remotes";

@Component({
  selector: "app-root",
  imports: [
    CommonModule,
    HomeComponent,
    ProfilesComponent,
    ProfileEditComponent,
    RemotesComponent,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatTabsModule,
  ],
  templateUrl: "./app.component.html",
  styleUrl: "./app.component.css",
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent implements OnInit {
  Action = Action;

  readonly tab$ = new BehaviorSubject<Tab>("home");
  isDarkMode = false;

  private changeDetectorSub: Subscription | undefined;

  constructor(
    public readonly appService: AppService,
    private readonly cdr: ChangeDetectorRef,
    public readonly navigationService: NavigationService
  ) {
    // Initialize dark mode from system preference
    this.isDarkMode = window.matchMedia("(prefers-color-scheme: dark)").matches;
    this.applyTheme();
  }

  ngOnInit() {
    this.changeDetectorSub = combineLatest([
      this.appService.currentAction$,
      this.tab$,
      this.navigationService.currentState$,
    ]).subscribe(() => this.cdr.detectChanges());

    // Sync navigation state with tab state
    this.tab$.subscribe((tab) => {
      if (tab === "profiles") {
        this.navigationService.navigateToProfiles();
      } else if (tab === "remotes") {
        this.navigationService.navigateToRemotes();
      } else if (tab === "home") {
        this.navigationService.navigateToHome();
      }
    });
  }

  async pull(profile: models.Profile) {
    this.tab$.next("home");
    this.appService.pull(profile);
  }

  async push(profile: models.Profile) {
    this.tab$.next("home");
    this.appService.push(profile);
  }

  async bi(profile: models.Profile) {
    this.tab$.next("home");
    this.appService.bi(profile);
  }

  stopCommand() {
    this.appService.stopCommand();
  }

  openHome() {
    this.tab$.next("home");
  }

  openProfiles() {
    this.tab$.next("profiles");
  }

  openRemotes() {
    this.tab$.next("remotes");
  }

  onTabChange(index: number) {
    const tabs: Tab[] = ["home", "profiles", "remotes"];
    this.tab$.next(tabs[index]);
  }

  toggleDarkMode() {
    this.isDarkMode = !this.isDarkMode;
    this.applyTheme();
    this.cdr.detectChanges();
  }

  private applyTheme() {
    const body = document.body;
    if (this.isDarkMode) {
      body.classList.add("dark-theme");
    } else {
      body.classList.remove("dark-theme");
    }
  }
}
